import 'package:equalcash/core/core.dart';

AuthUser authUserFromJson(String str) => AuthUser.fromJson(json.decode(str));

String authUserToJson(AuthUser data) => json.encode(data.toJson());

class AuthUser {
  final int? id;
  final String? firstName;
  final String? middleName;
  final String? lastName;
  final String? userName;
  final String? email;
  final dynamic emailVerifiedAt;
  final String? phone;
  final DateTime? phoneVerifiedAt;
  final DateTime? loggedInAt;
  final dynamic loggedOutAt;
  final dynamic passwordChangedAt;
  final String? status;
  final bool? isSuspended;
  final String? avatarUrl;
  final DateTime? dateOfBirth;
  final String? gender;
  final String? occupation;
  final String? referralCode;
  final String? homeAddress;
  final String? city;
  final String? state;
  final dynamic country;
  final String? postalCode;
  final int? kycStep;
  final AccountLimit? accountLimit;
  final bool? hasTransactionPin;
  final bool? hasNairaWallet;
  final bool? hasCanadianDollarWallet;
  final bool? isTrustedDevice;
  final String? politicallyExposed;
  final BankAccountModel? bankAccount;
  final DateTime? createdAt;

  AuthUser({
    this.id,
    this.firstName,
    this.middleName,
    this.lastName,
    this.userName,
    this.email,
    this.emailVerifiedAt,
    this.phone,
    this.phoneVerifiedAt,
    this.loggedInAt,
    this.loggedOutAt,
    this.passwordChangedAt,
    this.status,
    this.isSuspended,
    this.avatarUrl,
    this.dateOfBirth,
    this.gender,
    this.occupation,
    this.referralCode,
    this.homeAddress,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.kycStep,
    this.accountLimit,
    this.hasTransactionPin,
    this.hasNairaWallet,
    this.hasCanadianDollarWallet,
    this.isTrustedDevice,
    this.politicallyExposed,
    this.bankAccount,
    this.createdAt,
  });

  AuthUser copyWith({
    int? id,
    String? firstName,
    String? middleName,
    String? lastName,
    String? userName,
    String? email,
    dynamic emailVerifiedAt,
    String? phone,
    DateTime? phoneVerifiedAt,
    DateTime? loggedInAt,
    dynamic loggedOutAt,
    dynamic passwordChangedAt,
    String? status,
    bool? isSuspended,
    String? avatarUrl,
    DateTime? dateOfBirth,
    String? gender,
    String? occupation,
    String? referralCode,
    String? homeAddress,
    String? city,
    String? state,
    dynamic country,
    String? postalCode,
    int? kycStep,
    AccountLimit? accountLimit,
    bool? hasTransactionPin,
    bool? hasNairaWallet,
    bool? hasCanadianDollarWallet,
    bool? isTrustedDevice,
    String? politicallyExposed,
    BankAccountModel? bankAccount,
    DateTime? createdAt,
  }) =>
      AuthUser(
        id: id ?? this.id,
        firstName: firstName ?? this.firstName,
        middleName: middleName ?? this.middleName,
        lastName: lastName ?? this.lastName,
        userName: userName ?? this.userName,
        email: email ?? this.email,
        emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
        phone: phone ?? this.phone,
        phoneVerifiedAt: phoneVerifiedAt ?? this.phoneVerifiedAt,
        loggedInAt: loggedInAt ?? this.loggedInAt,
        loggedOutAt: loggedOutAt ?? this.loggedOutAt,
        passwordChangedAt: passwordChangedAt ?? this.passwordChangedAt,
        status: status ?? this.status,
        isSuspended: isSuspended ?? this.isSuspended,
        avatarUrl: avatarUrl ?? this.avatarUrl,
        dateOfBirth: dateOfBirth ?? this.dateOfBirth,
        gender: gender ?? this.gender,
        occupation: occupation ?? this.occupation,
        referralCode: referralCode ?? this.referralCode,
        homeAddress: homeAddress ?? this.homeAddress,
        city: city ?? this.city,
        state: state ?? this.state,
        country: country ?? this.country,
        postalCode: postalCode ?? this.postalCode,
        kycStep: kycStep ?? this.kycStep,
        accountLimit: accountLimit ?? this.accountLimit,
        hasTransactionPin: hasTransactionPin ?? this.hasTransactionPin,
        hasNairaWallet: hasNairaWallet ?? this.hasNairaWallet,
        hasCanadianDollarWallet:
            hasCanadianDollarWallet ?? this.hasCanadianDollarWallet,
        isTrustedDevice: isTrustedDevice ?? this.isTrustedDevice,
        politicallyExposed: politicallyExposed ?? this.politicallyExposed,
        bankAccount: bankAccount ?? this.bankAccount,
        createdAt: createdAt ?? this.createdAt,
      );

  factory AuthUser.fromJson(Map<String, dynamic> json) => AuthUser(
        id: json["id"],
        firstName: json["first_name"],
        middleName: json["middle_name"],
        lastName: json["last_name"],
        userName: json["user_name"],
        email: json["email"],
        emailVerifiedAt: json["email_verified_at"],
        phone: json["phone"],
        phoneVerifiedAt: json["phone_verified_at"] == null
            ? null
            : DateTime.parse(json["phone_verified_at"]),
        loggedInAt: json["logged_in_at"] == null
            ? null
            : DateTime.parse(json["logged_in_at"]),
        loggedOutAt: json["logged_out_at"],
        passwordChangedAt: json["password_changed_at"],
        status: json["status"],
        isSuspended: json["is_suspended"],
        avatarUrl: json["avatar_url"],
        dateOfBirth: json["date_of_birth"] == null
            ? null
            : DateTime.parse(json["date_of_birth"]),
        gender: json["gender"],
        occupation: json["occupation"],
        referralCode: json["referral_code"],
        homeAddress: json["home_address"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        postalCode: json["postal_code"],
        kycStep: json["kyc_step"],
        accountLimit: json["account_limit"] == null
            ? null
            : AccountLimit.fromJson(json["account_limit"]),
        hasTransactionPin: json["has_transaction_pin"],
        hasNairaWallet: json["has_naira_wallet"],
        hasCanadianDollarWallet: json["has_canadian_dollar_wallet"],
        isTrustedDevice: json["is_trusted_device"],
        politicallyExposed: json["politically_exposed"],
        bankAccount: json["bank_account"] == null
            ? null
            : BankAccountModel.fromJson(json["bank_account"]),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "first_name": firstName,
        "middle_name": middleName,
        "last_name": lastName,
        "user_name": userName,
        "email": email,
        "email_verified_at": emailVerifiedAt,
        "phone": phone,
        "phone_verified_at": phoneVerifiedAt?.toIso8601String(),
        "logged_in_at": loggedInAt?.toIso8601String(),
        "logged_out_at": loggedOutAt,
        "password_changed_at": passwordChangedAt,
        "status": status,
        "is_suspended": isSuspended,
        "avatar_url": avatarUrl,
        "date_of_birth":
            "${dateOfBirth!.year.toString().padLeft(4, '0')}-${dateOfBirth!.month.toString().padLeft(2, '0')}-${dateOfBirth!.day.toString().padLeft(2, '0')}",
        "gender": gender,
        "occupation": occupation,
        "referral_code": referralCode,
        "home_address": homeAddress,
        "city": city,
        "state": state,
        "country": country,
        "postal_code": postalCode,
        "kyc_step": kycStep,
        "account_limit": accountLimit?.toJson(),
        "has_transaction_pin": hasTransactionPin,
        "has_naira_wallet": hasNairaWallet,
        "has_canadian_dollar_wallet": hasCanadianDollarWallet,
        "is_trusted_device": isTrustedDevice,
        "politically_exposed": politicallyExposed,
        "bank_account": bankAccount?.toJson(),
        "created_at": createdAt?.toIso8601String(),
      };
}

class AccountLimit {
  final Limit? cadLimit;
  final Limit? ngnLimit;

  AccountLimit({
    this.cadLimit,
    this.ngnLimit,
  });

  AccountLimit copyWith({
    Limit? cadLimit,
    Limit? ngnLimit,
  }) =>
      AccountLimit(
        cadLimit: cadLimit ?? this.cadLimit,
        ngnLimit: ngnLimit ?? this.ngnLimit,
      );

  factory AccountLimit.fromJson(Map<String, dynamic> json) => AccountLimit(
        cadLimit: json["cad_limit"] == null
            ? null
            : Limit.fromJson(json["cad_limit"]),
        ngnLimit: json["ngn_limit"] == null
            ? null
            : Limit.fromJson(json["ngn_limit"]),
      );

  Map<String, dynamic> toJson() => {
        "cad_limit": cadLimit?.toJson(),
        "ngn_limit": ngnLimit?.toJson(),
      };
}

class Limit {
  final int? dailyUsage;
  final int? dailyLimit;

  Limit({
    this.dailyUsage,
    this.dailyLimit,
  });

  Limit copyWith({
    int? dailyUsage,
    int? dailyLimit,
  }) =>
      Limit(
        dailyUsage: dailyUsage ?? this.dailyUsage,
        dailyLimit: dailyLimit ?? this.dailyLimit,
      );

  factory Limit.fromJson(Map<String, dynamic> json) => Limit(
        dailyUsage: json["daily_usage"],
        dailyLimit: json["daily_limit"],
      );

  Map<String, dynamic> toJson() => {
        "daily_usage": dailyUsage,
        "daily_limit": dailyLimit,
      };
}
