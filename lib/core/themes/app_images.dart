import 'package:flutter/material.dart';

const String images = "assets/images";

class AppImages {
  AppImages._();

  // static const logo = "$images/logo.png";
  static const noImage = "$images/noImage.jpg";

  // Onboarding
  static const logo = "$images/logo.png";
  static const onb1 = "$images/onb1.png";

  static const emojiPerson = "$images/emojiPerson.png";
  static const bellEmoji = "$images/bellEmoji.png";
  static const solarFace = "$images/solarFace.png";
  static const accountCreated = "$images/accountCreated.png";
  static const lockVerified = "$images/lockVerified.png";
  static const card = "$images/card.png";
}

// Image Helper
SizedBox imageHelper(String image,
    {double? height, double? width, BoxFit? fit}) {
  return SizedBox(
    height: height,
    width: width,
    child: Image.asset(
      image,
      fit: fit,
    ),
  );
}
