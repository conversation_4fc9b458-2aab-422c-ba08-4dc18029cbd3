import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class OrderOfCollectionScreen extends StatefulWidget {
  const OrderOfCollectionScreen({super.key});

  @override
  State<OrderOfCollectionScreen> createState() =>
      _OrderOfCollectionScreenState();
}

class _OrderOfCollectionScreenState extends State<OrderOfCollectionScreen> {
  bool isSwitchedToTrade = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: CustomHeader(
        onTap: () {
          if (isSwitchedToTrade) {
            isSwitchedToTrade = false;
            setState(() {});
          } else {
            Navigator.pop(context);
          }
        },
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          const CustomSubHeader(
            title: 'Order Of Collection',
            subtitle: "Unique Brothers (10 Members)",
          ),
          const YBox(24),
          Text(
            'Monthly order of collection',
            style: AppTypography.text18.copyWith(
              color: AppColors.neutral400,
              fontWeight: FontWeight.w500,
            ),
          ),
          const YBox(16),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 10,
            separatorBuilder: (_, __) => const YBox(18),
            itemBuilder: (ctx, i) {
              return CollectionListTile(
                titleText: 'IK008 (CEO) ',
                optionalTitleText: isSwitchedToTrade ? '(January)' : null,
                trailingText: isSwitchedToTrade ? 'Trade' : 'January (1st)',
                trailingColor:
                    isSwitchedToTrade ? AppColors.primaryPurple : null,
                trailingTap: isSwitchedToTrade
                    ? () {
                        Navigator.pushNamed(
                            context, RoutePath.tradeOrderOfCollectionScreen);
                      }
                    : null,
              );
            },
          ),
          YBox(isSwitchedToTrade ? 60 : 32),
          (isSwitchedToTrade)
              ? Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(16),
                    vertical: Sizer.height(8),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.secondary100,
                    borderRadius: BorderRadius.circular(
                      Sizer.radius(8),
                    ),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Iconsax.info_circle5,
                        color: AppColors.primaryPurple,
                        size: Sizer.radius(24),
                      ),
                      const XBox(8),
                      Expanded(
                        child: Text(
                          'Stakeholders only have the opportunity of trading three times',
                          style: AppTypography.text13.copyWith(
                            color: AppColors.neutral300,
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              : Row(
                  children: [
                    Expanded(
                      child: CustomBtn.solid(
                        text: 'Accept',
                        textStyle: AppTypography.text16.copyWith(
                          color: AppColors.white,
                        ),
                        onTap: () {},
                      ),
                    ),
                    const XBox(12),
                    Expanded(
                      child: CustomBtn.solid(
                        text: 'Trade',
                        textStyle: AppTypography.text16.copyWith(
                          color: AppColors.white,
                        ),
                        onlineColor: AppColors.primaryPurple.withOpacity(0.7),
                        onTap: () {
                          isSwitchedToTrade = true;
                          setState(() {});
                        },
                      ),
                    ),
                  ],
                ),
          const YBox(100),
        ],
      ),
    );
  }
}

class CollectionListTile extends StatelessWidget {
  const CollectionListTile({
    super.key,
    required this.titleText,
    this.optionalTitleText,
    required this.trailingText,
    this.trailingColor,
    this.trailingTap,
  });

  final String titleText;
  final String? optionalTitleText;
  final String trailingText;
  final Color? trailingColor;
  final VoidCallback? trailingTap;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              titleText,
              style: AppTypography.text16.copyWith(
                color: AppColors.primaryPurple,
              ),
            ),
            if (optionalTitleText != null && optionalTitleText != '')
              Text(
                optionalTitleText ?? '',
                style: AppTypography.text12.copyWith(
                  color: AppColors.neutral200,
                ),
              ),
          ],
        ),
        const Spacer(),
        InkWell(
          onTap: trailingTap,
          child: Text(
            trailingText,
            style: AppTypography.text14.copyWith(
              color: trailingColor ?? AppColors.neutral300,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}
