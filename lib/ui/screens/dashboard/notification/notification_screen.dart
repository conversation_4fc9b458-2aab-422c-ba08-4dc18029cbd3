import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class NotificationScreen extends ConsumerStatefulWidget {
  const NotificationScreen({super.key});

  @override
  ConsumerState<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends ConsumerState<NotificationScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(notificationVm).getAllNotifications(true);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final notificationRef = ref.watch(notificationVm);
        return Scaffold(
          appBar: CustomDashboardAppbar(
            showSearchField: false,
            height: 100,
            topWidget: Text(
              'Notifications',
              style: AppTypography.text24.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          body: Container(
            color: AppColors.purpleF1,
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: Column(
              children: [
                const YBox(16),
                Row(
                  children: [
                    MiniBtn(
                      text: 'Transactions',
                      fontSize: 14,
                      isSelected: true,
                      onTap: () {
                        setState(() {});
                      },
                    ),
                    const XBox(10),
                    MiniBtn(
                      text: 'Activities',
                      count: '1',
                      fontSize: 14,
                      onTap: () {
                        setState(() {});
                      },
                    )
                  ],
                ),
                Expanded(
                  child: LoadableContentBuilder(
                      isBusy: notificationRef.isBusy,
                      items: notificationRef.allNotifications,
                      loadingBuilder: (context) {
                        return ListView.separated(
                          padding: EdgeInsets.only(
                            top: Sizer.height(16),
                            bottom: Sizer.height(100),
                          ),
                          itemBuilder: (ctx, i) {
                            return Skeletonizer(
                              enabled: true,
                              child: NotificationTile(
                                title: "Contribution Company Invitation",
                                subtitle:
                                    "You are invited by Adebayo Salami to join a monthly contribution scheme...",
                                createdAt: DateTime.now(),
                              ),
                            );
                          },
                          separatorBuilder: (_, __) => const YBox(16),
                          itemCount: 8,
                        );
                      },
                      emptyBuilder: (context) {
                        return const EmptyListState(
                          text: 'No notifications found',
                        );
                      },
                      contentBuilder: (context) {
                        return ListView.builder(
                          padding: EdgeInsets.only(
                            top: Sizer.height(16),
                            bottom: Sizer.height(100),
                          ),
                          itemBuilder: (context, index) {
                            final notification =
                                notificationRef.allNotifications[index];
                            return Padding(
                              padding: EdgeInsets.only(bottom: 16.h),
                              child: NotificationTile(
                                title: notification?.subject,
                                subtitle: notification?.message,
                                createdAt: notification?.createdAt,
                                onTap: () {
                                  switch (notification?.type) {
                                    case "first_company_invitation":
                                      NavigationHelper.navigateTo(
                                        routeName:
                                            RoutePath.invitationDetailsScreen,
                                        args: notification?.typePath ?? '',
                                      );
                                      break;
                                    case "0":
                                      NavigationHelper.navigateTo(
                                        routeName: RoutePath.invitationScreen,
                                      );
                                      break;
                                    case "1":
                                      NavigationHelper.navigateTo(
                                        routeName:
                                            RoutePath.invitationDetailsScreen,
                                      );
                                      break;
                                    case "2":
                                      NavigationHelper.navigateTo(
                                        routeName:
                                            RoutePath.orderOfCollectionScreen,
                                      );
                                      break;
                                    case "3":
                                      NavigationHelper.navigateTo(
                                        routeName: RoutePath.debitCardScreen,
                                      );
                                      break;
                                    default:
                                  }
                                },
                              ),
                            );
                          },
                          itemCount: notificationRef.allNotifications.length,
                        );
                      }),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
