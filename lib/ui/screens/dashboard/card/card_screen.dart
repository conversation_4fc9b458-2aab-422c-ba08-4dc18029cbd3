import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class CardScreen extends ConsumerStatefulWidget {
  const CardScreen({super.key});

  @override
  ConsumerState<CardScreen> createState() => _DebitCardScreenState();
}

class _DebitCardScreenState extends ConsumerState<CardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(cardVmodel).getCards();
    });
  }

  @override
  Widget build(BuildContext context) {
    final cardRef = ref.watch(cardVmodel);
    return Consumer(builder: (context, ref, child) {
      return BusyOverlay(
        show: ref.watch(cardVmodel).isBusy,
        child: Scaffold(
          backgroundColor: AppColors.purpleF1,
          appBar: const CustomHeader(headerText: "Debit Card"),
          body: LoadableContentBuilder(
            isError: cardRef.hasError,
            isBusy: cardRef.isBusy,
            items: cardRef.cards,
            loadingBuilder: (context) {
              return ListView.separated(
                padding: EdgeInsets.only(
                  left: Sizer.width(16),
                  right: Sizer.width(16),
                  top: Sizer.height(24),
                  bottom: Sizer.height(100),
                ),
                itemCount: 5,
                separatorBuilder: (_, __) => const YBox(16),
                itemBuilder: (ctx, i) {
                  return const Skeletonizer(
                    enabled: true,
                    child: CreditCardView(
                      cardNumber: "5399 8282 8282 8210",
                      cardHolder: "John Doe",
                      expiryDate: "12/25",
                      cvv: "123",
                      backgroundImage: AppImages.card,
                      cardTypeLogo: AppSvgs.visa,
                    ),
                  );
                },
              );
            },
            errorBuilder: (context) {
              return EmptyListState(
                text: 'Failed to load bank accounts',
                onRetry: () {
                  ref.read(bankVmodel).getMyBankAccounts();
                },
              );
            },
            emptyBuilder: (context) {
              return Center(
                child: Text(
                  "No bank accounts found",
                  style: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.neutral300,
                  ),
                ),
              );
            },
            contentBuilder: (context) {
              return ListView.separated(
                padding: EdgeInsets.only(
                  left: Sizer.width(16),
                  right: Sizer.width(16),
                  top: Sizer.height(24),
                  bottom: Sizer.height(100),
                ),
                itemCount: cardRef.cards.length,
                separatorBuilder: (_, __) => const YBox(16),
                itemBuilder: (ctx, i) {
                  final card = cardRef.cards[i];
                  return CreditCardView(
                    cardNumber:
                        '****   ****   ****   ****   ${card.last4 ?? ""}',
                    cardHolder: card.accountName ?? "",
                    expiryDate:
                        "${card.expiryMonth ?? ""}/${card.expiryYear ?? ""}",
                    cvv: "123",
                    backgroundImage: AppImages.card,
                    cardTypeLogo: AppSvgs.visa,
                  );
                },
              );
            },
          ),
          bottomSheet: Container(
            color: AppColors.purpleF1,
            padding: EdgeInsets.only(
              left: Sizer.width(16),
              right: Sizer.width(16),
              bottom: Sizer.height(30),
            ),
            child: CustomBtn.solid(
              text: 'Add more cards',
              textStyle: AppTypography.text16.copyWith(
                color: AppColors.white,
              ),
              onTap: () {
                // Navigator.pushNamed(context, RoutePath.addCardScreen);
                ref.read(cardVmodel).initializeCardTransaction().then((value) {
                  handleApiResponse(
                    response: value,
                    onCompleted: () {
                      Navigator.pushNamed(
                        context,
                        RoutePath.customWebviewScreen,
                        arguments: WebViewArg(
                          webURL: value.data,
                        ),
                      );
                    },
                  );
                });
              },
            ),
          ),
        ),
      );
    });
  }
}
