import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class EditProfileScreen extends ConsumerStatefulWidget {
  const EditProfileScreen({super.key});

  @override
  ConsumerState<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  @override
  Widget build(BuildContext context) {
    final profileRef = ref.watch(profileVm);
    return BusyOverlay(
      show: profileRef.isBusy,
      child: Scaffold(
        backgroundColor: AppColors.purpleF1,
        appBar: const CustomHeader(),
        body: RefreshIndicator(
          onRefresh: () async {
            await profileRef.getUser();
          },
          child: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            children: [
              const YBox(20),
              const CustomSubHeader(
                title: 'Edit Profile',
                subtitle:
                    "Your new password must be different from \npreviously used passwords",
              ),
              const YBox(30),
              const AvatarWidget(
                borderColor: AppColors.transparent,
              ),
              const YBox(40),
              CustomTextField(
                labelText: 'User Name',
                hintText: profileRef.authUser?.lastName ?? '',
                showLabelHeader: true,
                isReadOnly: true,
                hintStyle: TextStyle(
                  fontSize: Sizer.text(16),
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral200,
                ),
                suffixIcon: const Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(20),
              CustomTextField(
                labelText: 'First Name',
                hintText: profileRef.authUser?.firstName ?? '',
                showLabelHeader: true,
                isReadOnly: true,
                hintStyle: TextStyle(
                  fontSize: Sizer.text(16),
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral200,
                ),
                suffixIcon: const Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(20),
              CustomTextField(
                labelText: 'Email',
                hintText: profileRef.authUser?.email ?? '',
                showLabelHeader: true,
                isReadOnly: true,
                hintStyle: TextStyle(
                  fontSize: Sizer.text(16),
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral200,
                ),
                suffixIcon: const Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(20),
              CustomTextField(
                labelText: 'Phone Number',
                hintText: profileRef.authUser?.phone ?? '',
                showLabelHeader: true,
                isReadOnly: true,
                hintStyle: TextStyle(
                  fontSize: Sizer.text(16),
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral200,
                ),
                suffixIcon: const Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(20),
              const CustomTextField(
                labelText: 'Date of Birth',
                hintText: 'DD/MM/YYYY',
                showLabelHeader: true,
                isReadOnly: true,
                suffixIcon: Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(100),
            ],
          ),
        ),
      ),
    );
  }
}
