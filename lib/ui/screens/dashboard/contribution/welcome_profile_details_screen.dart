import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class WelcomeProfileDetailsScreen extends ConsumerStatefulWidget {
  const WelcomeProfileDetailsScreen({super.key});

  @override
  ConsumerState<WelcomeProfileDetailsScreen> createState() =>
      _WelcomeProfileDetailsScreenState();
}

class _WelcomeProfileDetailsScreenState
    extends ConsumerState<WelcomeProfileDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    final profileRef = ref.watch(profileVm);
    return BusyOverlay(
      show: profileRef.isBusy,
      child: Scaffold(
        backgroundColor: AppColors.purpleF1,
        appBar: const CustomHeader(),
        body: RefreshIndicator(
          onRefresh: () async {
            await profileRef.getUser();
          },
          child: ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            children: [
              const YBox(20),
              const CustomSubHeader(
                title: 'Welcome!',
                subtitle:
                    "You have successfully joined the Unique \nBrothers! Please confirm the information below",
              ),
              const YBox(30),
              CustomTextField(
                labelText: 'First Name',
                hintText: profileRef.authUser?.firstName ?? '',
                showLabelHeader: true,
                isReadOnly: true,
                hintStyle: TextStyle(
                  fontSize: Sizer.text(16),
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral200,
                ),
                suffixIcon: const Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(20),
              CustomTextField(
                labelText: 'Email',
                hintText: profileRef.authUser?.email ?? '',
                showLabelHeader: true,
                isReadOnly: true,
                hintStyle: TextStyle(
                  fontSize: Sizer.text(16),
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral200,
                ),
                suffixIcon: const Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(20),
              CustomTextField(
                labelText: 'Phone Number',
                hintText: profileRef.authUser?.phone ?? '',
                showLabelHeader: true,
                isReadOnly: true,
                hintStyle: TextStyle(
                  fontSize: Sizer.text(16),
                  fontWeight: FontWeight.w400,
                  color: AppColors.neutral200,
                ),
                suffixIcon: const Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(20),
              const CustomTextField(
                labelText: 'Address',
                hintText: 'DD/MM/YYYY',
                showLabelHeader: true,
                isReadOnly: true,
                suffixIcon: Icon(
                  Iconsax.lock_1,
                ),
              ),
              const YBox(100),
            ],
          ),
        ),
      ),
    );
  }
}
