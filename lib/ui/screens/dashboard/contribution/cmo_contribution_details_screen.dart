import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class CmoContributionDetailsScreen extends StatefulWidget {
  const CmoContributionDetailsScreen({
    super.key,
    required this.company,
  });

  final CompanyModel company;

  @override
  State<CmoContributionDetailsScreen> createState() =>
      _CmoContributionDetailsScreenState();
}

class _CmoContributionDetailsScreenState
    extends State<CmoContributionDetailsScreen> {
  InvitationType invitationType = InvitationType.accepted;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.purpleF1,
      appBar: const CustomHeader(),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        children: [
          const YBox(20),
          CustomSubHeader(
            title: '${widget.company.name}',
            subtitle:
                'Created by ${widget.company.ceo?.firstName ?? ''} ${widget.company.ceo?.lastName ?? ''}',
          ),
          const YBox(30),
          ColumnText(
            title: 'Company’s Name',
            subtitle: '${widget.company.name}',
          ),
          const YBox(18),
          ColumnText(
            title: 'Cash Bucket (Targeted amount)',
            subtitle: "${AppConst.naira}${AppUtils.formatNumber(
              number: double.tryParse(widget.company.cashBucket ?? '0') ?? 0,
              decimalPlaces: 2,
            )}",
          ),
          const YBox(18),
          ColumnText(
            title: 'Cash Drop (Monthly contribution per person)',
            subtitle: "${AppConst.naira}${AppUtils.formatNumber(
              number: double.tryParse(widget.company.cashDrop ?? '0') ?? 0,
              decimalPlaces: 2,
            )}",
          ),
          const YBox(18),
          ColumnText(
            title: 'Duration',
            subtitle: '${widget.company.duration} months',
          ),
          const YBox(18),
          ColumnText(
            title: 'Commencement Date',
            subtitle: AppUtils.daymy(
                widget.company.commencementDate ?? DateTime.now()),
          ),
          const YBox(18),
          ColumnText(
            title: 'Number of Stakeholders',
            subtitle: '${widget.company.numberOfStakeholders} members',
          ),
          const YBox(60),
          Row(
            children: [
              Expanded(
                child: CustomBtn.solid(
                  text: 'Join Company',
                  textStyle: AppTypography.text16.copyWith(
                    color: AppColors.white,
                  ),
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      RoutePath.welcomeProfileDetailsScreen,
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
