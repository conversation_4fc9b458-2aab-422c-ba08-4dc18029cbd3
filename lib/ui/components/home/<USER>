import 'package:equalcash/core/core.dart';

class HomeCashBucketCard extends StatelessWidget {
  const HomeCashBucketCard({
    super.key,
    this.onTap,
    this.company,
  });

  final Function()? onTap;
  final CompanyModel? company;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        NavigationHelper.navigateTo(
          routeName: (company?.authUserIsCeo ?? false)
              ? RoutePath.contributionDetailsScreen
              : RoutePath.cmoContributionDetailsScreen,
          args: company,
        );
      },
      child: Container(
        padding: EdgeInsets.all(
          Sizer.radius(16),
        ),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(
            Sizer.radius(12),
          ),
          border: Border.all(
            color: AppColors.secondary100,
            width: 0,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: company?.name ?? '',
                    style: AppTypography.text16.copyWith(
                      color: AppColors.primaryPurple,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'Nohemi',
                    ),
                  ),
                  TextSpan(
                    text: ' (${company?.numberOfStakeholders ?? 0} members)',
                    style: AppTypography.text14.copyWith(
                      color: AppColors.pri300,
                      fontFamily: 'Nohemi',
                    ),
                  ),
                ],
              ),
            ),
            const YBox(8),
            Row(
              children: [
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'Cash Bucket: ',
                        style: AppTypography.text12.copyWith(
                          color: AppColors.neutral300,
                          fontFamily: 'Nohemi',
                        ),
                      ),
                      TextSpan(
                        text: AppConst.naira,
                        style: AppTypography.text12.copyWith(
                          color: AppColors.neutral400,
                          // fontFamily: 'Nohemi',
                        ),
                      ),
                      TextSpan(
                        text: AppUtils.formatNumber(
                            number:
                                double.tryParse(company?.cashBucket ?? '0') ??
                                    0),
                        style: AppTypography.text12.copyWith(
                          color: AppColors.neutral400,
                          fontFamily: 'Nohemi',
                        ),
                      ),
                    ],
                  ),
                ),
                const RichTextDot(),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'Cash Drop: ',
                        style: AppTypography.text12.copyWith(
                          color: AppColors.neutral300,
                          fontFamily: 'Nohemi',
                        ),
                      ),
                      TextSpan(
                        text: AppConst.naira,
                        style: AppTypography.text12.copyWith(
                          color: AppColors.neutral400,
                          // fontFamily: 'Nohemi',
                        ),
                      ),
                      TextSpan(
                        text: AppUtils.formatNumber(
                            number:
                                double.tryParse(company?.cashDrop ?? '0') ?? 0),
                        style: AppTypography.text12.copyWith(
                          color: AppColors.neutral400,
                          fontFamily: 'Nohemi',
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const YBox(8),
            Row(
              children: [
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'Start Date: ',
                        style: AppTypography.text12.copyWith(
                          color: AppColors.neutral300,
                          fontFamily: 'Nohemi',
                        ),
                      ),
                      TextSpan(
                        text: AppUtils.daymy(
                            company?.commencementDate ?? DateTime.now()),
                        style: AppTypography.text12.copyWith(
                          color: AppColors.neutral400,
                          fontFamily: 'Nohemi',
                        ),
                      ),
                    ],
                  ),
                ),
                const RichTextDot(),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: 'Duration ',
                        style: AppTypography.text12.copyWith(
                          color: AppColors.neutral300,
                          fontFamily: 'Nohemi',
                        ),
                      ),
                      TextSpan(
                        text: '${company?.duration ?? 0} months',
                        style: AppTypography.text12.copyWith(
                          color: AppColors.neutral400,
                          fontFamily: 'Nohemi',
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}

class RichTextDot extends StatelessWidget {
  const RichTextDot({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(12),
      ),
      child: Container(
        height: Sizer.height(4),
        width: Sizer.width(4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            Sizer.radius(20),
          ),
          color: AppColors.neutral200,
        ),
      ),
    );
  }
}
