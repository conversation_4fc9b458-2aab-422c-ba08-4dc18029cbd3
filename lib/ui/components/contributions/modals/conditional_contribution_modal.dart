import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class ConditionalContributionModal extends StatelessWidget {
  const ConditionalContributionModal({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(24),
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(Sizer.radius(24)),
          topRight: Radius.circular(Sizer.radius(24)),
        ),
        color: AppColors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(16),
          Align(
            alignment: Alignment.centerLeft,
            child: InkWell(
              onTap: () => Navigator.pop(context),
              child: Icon(
                Icons.close,
                size: Sizer.radius(28),
              ),
            ),
          ),
          const YBox(30),
          Text(
            'IK008',
            style: AppTypography.text16.copyWith(
              color: AppColors.primaryPurple,
            ),
          ),
          const YBox(8),
          Text(
            'Add a conditional comment to the Terms & \ncondition of this contribution',
            style: AppTypography.text13.copyWith(
              color: AppColors.neutral300,
              fontWeight: FontWeight.w400,
              height: 1.5,
            ),
          ),
          const YBox(40),
          const CustomTextField(
            hintText: 'Add comment',
            maxLines: 4,
          ),
          const YBox(40),
          CustomBtn.solid(
            text: 'Submit Condition',
            onTap: () {
              ModalWrapper.bottomSheet(
                context: context,
                widget: InfoActionModal(
                  title: 'Condition successfully submitted',
                  content:
                      'Declining will take you out of the Unique brothers company',
                  btnText: 'Proceed To Dashboard',
                  onAction: () {},
                ),
              );
            },
          ),
          const YBox(40),
        ],
      ),
    );
  }
}
