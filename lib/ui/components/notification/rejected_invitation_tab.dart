import 'package:equalcash/core/core.dart';
import 'package:equalcash/ui/components/components.dart';

class RejectedInvitationTab extends StatefulWidget {
  const RejectedInvitationTab({
    super.key,
  });

  @override
  State<RejectedInvitationTab> createState() => _RejectedInvitationTabState();
}

class _RejectedInvitationTabState extends State<RejectedInvitationTab> {
  bool _showInviteTextField = false;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Accepted Invitations (6)',
          style: AppTypography.text18.copyWith(
            color: AppColors.neutral400,
          ),
        ),
        const YBox(12),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (ctx, i) {
            return const InviteTile(
              invitee: '<EMAIL>',
              status: 'Rejected',
            );
          },
          separatorBuilder: (_, __) => const YBox(24),
          itemCount: 4,
        ),
        if (!_showInviteTextField) const YBox(64),
        if (!_showInviteTextField)
          CustomBtn.withChild(
            online: true,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(
                  Iconsax.add_circle,
                  color: AppColors.white,
                  size: Sizer.radius(24),
                ),
                const XBox(8),
                Padding(
                  padding: EdgeInsets.only(top: Sizer.height(4)),
                  child: Text(
                    'Add more invite',
                    style: AppTypography.text18.copyWith(
                      color: AppColors.white,
                    ),
                  ),
                ),
              ],
            ),
            onTap: () {
              _showInviteTextField = true;
              setState(() {});
            },
          ),
        if (_showInviteTextField) const InviteTexField(),
      ],
    );
  }
}

class InviteTexField extends StatelessWidget {
  const InviteTexField({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const YBox(32),
        Text(
          'You have 4 rejection! Send out more invite \nto your contact',
          style: AppTypography.text14.copyWith(
            color: AppColors.neutral300,
          ),
        ),
        const YBox(24),
        const CustomTextField(
          // controller: occupationC,
          // focusNode: occupationF,
          labelText: 'Invite by phone number or email',
          showLabelHeader: true,
        ),
        const YBox(24),
        CustomBtn.solid(
          text: 'Send Invite',
          onTap: () {
            ModalWrapper.bottomSheet(
              context: context,
              widget: InfoActionModal(
                icon: AppSvgs.success,
                title: 'Invitation Sent Successful',
                content:
                    'Congratulations! Your account has been successfully verified',
                btnText: 'Proceed To Dashboard',
                onAction: () {},
              ),
            );
          },
        ),
        const YBox(24),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            InkWell(
              onTap: () {
                ModalWrapper.bottomSheet(
                  context: context,
                  widget: InfoActionModal(
                    title: 'Invitation Link Copied',
                    content:
                        'To proceed and create a contribution company, please complete the KYC',
                    btnText: 'Proceed To Dashboard',
                    onAction: () {},
                  ),
                );
              },
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  svgHelper(AppSvgs.link),
                  const XBox(4),
                  Text(
                    'Copy link',
                    style: AppTypography.text14.copyWith(
                      color: AppColors.purpleB0,
                    ),
                  ),
                ],
              ),
            ),
          ],
        )
      ],
    );
  }
}
