import 'package:equalcash/core/core.dart';

class TakePhotoModal extends StatefulWidget {
  const TakePhotoModal({super.key});

  @override
  State<TakePhotoModal> createState() => _TakePhotoModalState();
}

class _TakePhotoModalState extends State<TakePhotoModal> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: Sizer.width(30),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(30),
        child: Container(
          width: MediaQuery.of(context).size.width,
          height: Sizer.height(160),
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(30),
            // vertical: Sizer.height(16),
          ),
          color: Colors.white,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _actionText('Take photo', () {}),
              const YBox(16),
              _actionText('Choose from gallery', () {}),
            ],
          ),
        ),
      ),
    );
  }

  Widget _actionText(String text, Function() onTap) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(5),
        ),
        child: Text(
          text,
          style: AppTypography.text16.copyWith(
            color: AppColors.neutral300,
          ),
        ),
      ),
    );
  }
}
